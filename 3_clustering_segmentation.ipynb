# Imports spécifiques pour ce notebook
import os
import json
from datetime import datetime
import joblib

# Clustering et métriques
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.preprocessing import StandardScaler
from itertools import combinations
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy import stats

# Imports locaux - modules utils optimisés
from utils.core import (
    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,
    # Les imports de base sont déjà dans core
    pd, np, plt, sns
)
from utils.clustering import (
    find_optimal_k,  # au lieu de find_optimal_k_elbow
    perform_kmeans,
    perform_dbscan,
    calculate_clustering_metrics,
    analyze_clusters,
    compare_clustering_algorithms
)  # Suppression de la parenthèse supplémentaire ici
from utils.clustering_visualization import (
    plot_elbow_curve,
    plot_clusters_2d,
    plot_cluster_profiles,
    plot_cluster_feature_distributions,
    plot_cluster_sizes,
    export_figure
)
from utils.data_tools import load_data, export_artifact
from utils.save_load import save_results, load_results

# Configuration du notebook avec le module core
init_notebook(
    notebook_file_path="3_clustering_segmentation.ipynb",
    style="whitegrid",
    figsize=(12, 8),
    random_seed=SEED,
    setup=True,
    check_deps=True
)

# Chargement des données préparées du Notebook 2
print("🔄 Chargement des données préparées pour le clustering...")

# Chemins des fichiers générés par le notebook 2
data_path_scaled = 'data/processed/2_01_features_scaled_clustering.csv'
data_path_with_ids = 'data/processed/2_02_features_scaled_with_ids.csv'
data_path_complete = 'data/processed/2_03_rfm_enriched_complete.csv'

try:
    # Chargement des datasets
    X_scaled = load_results(data_path_scaled)
    X_with_ids = load_results(data_path_with_ids)
    df_complete = load_results(data_path_complete)

    print(f"✅ Dataset pour clustering : {X_scaled.shape}")
    print(f"   Variables : {list(X_scaled.columns)}")
    print(f"\n✅ Dataset avec IDs : {X_with_ids.shape}")
    print(f"✅ Dataset complet : {df_complete.shape}")

    # Vérification de la cohérence
    assert len(X_scaled) == len(X_with_ids) == len(df_complete), "Tailles incohérentes entre les datasets"
    print(f"\n✅ Cohérence vérifiée : {len(X_scaled)} clients dans tous les datasets")

except FileNotFoundError as e:
    print(f"❌ Erreur : Fichier non trouvé - {e}")
    print("💡 Assurez-vous d'avoir exécuté le Notebook 2 (Feature Engineering) avant ce notebook.")
    raise
except Exception as e:
    print(f"❌ Erreur lors du chargement : {e}")
    raise

# 🚨 DIAGNOSTIC CRITIQUE : Adaptation selon la stratégie "First Purchase"
print("🚨 DIAGNOSTIC CRITIQUE DES VARIABLES ACTUELLES")
print("=" * 60)

print(f"\n📊 Variables actuellement chargées : {len(X_scaled.columns)}")
for i, col in enumerate(X_scaled.columns, 1):
    print(f"{i:2d}. {col}")

# Identification des variables problématiques selon la stratégie
print("\n🔍 ANALYSE DES VARIABLES SELON LA STRATÉGIE :")

# Variables constantes (variance nulle)
constant_variables = X_scaled.columns[X_scaled.std() == 0].tolist()
print(f"\n❌ Variables à variance nulle ({len(constant_variables)}) :")
for var in constant_variables:
    print(f"   - {var} : valeur = {X_scaled[var].iloc[0]}")

# Variables redondantes identifiées dans la stratégie
redundant_vars = [
    'total_amount', 'amount_total', 'avg_amount', 'montant_moyen',  # Doublons monetary
    'total_orders', 'order_count',  # Doublons frequency
    'recency_days', 'days_since_first_order',  # Doublons recency
    'amount_std', 'amount_std_dev',  # Doublons std
    'avg_order_value', 'order_value_mean',  # Doublons
    'amount_cv', 'amount_cv_coef',  # Variance nulle
    'customer_lifespan_days',  # Toujours 0
    'purchase_frequency'  # Pas de sens avec 1 commande
]

present_redundant = [var for var in redundant_vars if var in X_scaled.columns]
print(f"\n❌ Variables redondantes présentes ({len(present_redundant)}) :")
for var in present_redundant:
    print(f"   - {var}")

# Variables pertinentes selon la stratégie "First Purchase"
target_variables = [
    'recency',           # Récence (jours depuis achat)
    'monetary'           # Montant de la commande
]

present_target = [var for var in target_variables if var in X_scaled.columns]
print(f"\n✅ Variables pertinentes présentes ({len(present_target)}) :")
for var in present_target:
    print(f"   - {var}")

print(f"\n🎯 CONCLUSION STRATÉGIQUE :")
print(f"   - Variables actuelles : {len(X_scaled.columns)}")
print(f"   - Variables à éliminer : {len(present_redundant) + len(constant_variables)}")
print(f"   - Variables pertinentes : {len(present_target)}")
print(f"   - Variables manquantes : Contextuelles (géographie, saisonnalité, satisfaction)")

print("\n⚠️ NÉCESSITÉ D'ADAPTATION :")
print("   Le dataset actuel contient trop de variables redondantes.")
print("   Nous devons charger les variables contextuelles du dataset complet.")

# 🎯 CRÉATION DES VARIABLES "FIRST PURCHASE" SELON LA STRATÉGIE
print("🎯 CRÉATION DES VARIABLES ADAPTÉES AU CONTEXTE MONO-ACHAT")
print("=" * 65)

# Extraction des variables contextuelles du dataset complet
print("\n📊 Variables disponibles dans le dataset complet :")
print(f"   Colonnes : {list(df_complete.columns)}")

# Création des variables "First Purchase" selon la stratégie
print("\n🔧 Création des variables selon la stratégie :")

# Variables de base (déjà normalisées)
first_purchase_features = pd.DataFrame(index=df_complete.index)

# 1. Récence (variable principale)
first_purchase_features['recency'] = X_scaled['recency']
print("   ✅ recency : Jours depuis l'achat (normalisé)")

# 2. Montant (variable principale)
first_purchase_features['monetary'] = X_scaled['monetary']
print("   ✅ monetary : Montant de la commande (normalisé)")

# 3. Variables contextuelles à créer depuis le dataset complet
if 'customer_state' in df_complete.columns:
    # Géographie : encodage des états
    from sklearn.preprocessing import LabelEncoder
    le_state = LabelEncoder()
    state_encoded = le_state.fit_transform(df_complete['customer_state'].fillna('Unknown'))
    # Normalisation
    from sklearn.preprocessing import StandardScaler
    scaler_state = StandardScaler()
    first_purchase_features['state_encoded'] = scaler_state.fit_transform(state_encoded.reshape(-1, 1)).flatten()
    print("   ✅ state_encoded : Localisation géographique (normalisé)")
else:
    print("   ❌ customer_state non disponible")

if 'order_purchase_timestamp' in df_complete.columns:
    # Saisonnalité : mois d'achat
    purchase_month = pd.to_datetime(df_complete['order_purchase_timestamp']).dt.month
    scaler_month = StandardScaler()
    first_purchase_features['purchase_month'] = scaler_month.fit_transform(purchase_month.values.reshape(-1, 1)).flatten()
    print("   ✅ purchase_month : Saisonnalité (normalisé)")
else:
    print("   ❌ order_purchase_timestamp non disponible")

if 'order_delivered_customer_date' in df_complete.columns and 'order_purchase_timestamp' in df_complete.columns:
    # Performance logistique : délai de livraison
    delivery_date = pd.to_datetime(df_complete['order_delivered_customer_date'])
    purchase_date = pd.to_datetime(df_complete['order_purchase_timestamp'])
    delivery_days = (delivery_date - purchase_date).dt.days
    delivery_days_clean = delivery_days.fillna(delivery_days.median())
    scaler_delivery = StandardScaler()
    first_purchase_features['delivery_days'] = scaler_delivery.fit_transform(delivery_days_clean.values.reshape(-1, 1)).flatten()
    print("   ✅ delivery_days : Délai de livraison (normalisé)")
else:
    print("   ❌ Données de livraison non disponibles")

if 'review_score' in df_complete.columns:
    # Satisfaction client
    review_score_clean = df_complete['review_score'].fillna(df_complete['review_score'].median())
    scaler_review = StandardScaler()
    first_purchase_features['review_score'] = scaler_review.fit_transform(review_score_clean.values.reshape(-1, 1)).flatten()
    print("   ✅ review_score : Satisfaction client (normalisé)")
else:
    print("   ❌ review_score non disponible")

# Nettoyage des valeurs manquantes
first_purchase_features = first_purchase_features.dropna()

print(f"\n📈 DATASET FINAL POUR CLUSTERING :")
print(f"   - Observations : {len(first_purchase_features):,}")
print(f"   - Variables : {len(first_purchase_features.columns)}")
print(f"   - Variables utilisées : {list(first_purchase_features.columns)}")

# Vérification de la qualité
print(f"\n🔍 VÉRIFICATION DE LA QUALITÉ :")
print(f"   - Valeurs manquantes : {first_purchase_features.isnull().sum().sum()}")
print(f"   - Variables constantes : {(first_purchase_features.std() == 0).sum()}")
print(f"   - Corrélations élevées (>0.7) : {((first_purchase_features.corr().abs() > 0.7) & (first_purchase_features.corr().abs() < 1.0)).sum().sum() // 2}")

# Mise à jour des datasets pour la suite
X_scaled_adapted = first_purchase_features.copy()
print(f"\n✅ Dataset adapté créé : X_scaled_adapted ({X_scaled_adapted.shape})")

# Affichage des statistiques descriptives
print(f"\n📊 Statistiques descriptives des variables adaptées :")
display(X_scaled_adapted.describe().round(3))

# 🔍 RECHERCHE DU NOMBRE OPTIMAL DE CLUSTERS - VARIABLES ADAPTÉES
print("🔍 Recherche du nombre optimal avec les variables \"First Purchase\"")
print("=" * 65)

# Vérification du dataset adapté
if 'X_scaled_adapted' not in locals():
    print("❌ Erreur : Variables adaptées non créées. Exécutez la section 1.4 d'abord.")
    raise ValueError("Dataset adapté manquant")

print(f"\n📊 Dataset utilisé : {X_scaled_adapted.shape}")
print(f"   Variables : {list(X_scaled_adapted.columns)}")

# Configuration pour optimiser les performances
k_range = range(2, 11)  # Test de 2 à 10 clusters
inertias = []
silhouette_scores = []

# Échantillonnage pour le score de silhouette (plus rapide)
sample_size = min(10000, len(X_scaled_adapted))
X_sample_adapted = X_scaled_adapted.sample(n=sample_size, random_state=SEED)
print(f"\n📊 Utilisation d'un échantillon de {sample_size} points pour le score de silhouette")

print("\nCalcul en cours...")
for k in k_range:
    print(f"  K = {k}", end=" ")

    # K-Means avec paramètres optimisés
    kmeans = KMeans(
        n_clusters=k,
        random_state=SEED,
        n_init=5,  # Réduit pour optimiser
        max_iter=300,
        algorithm='elkan'  # Plus rapide pour datasets denses
    )
    cluster_labels = kmeans.fit_predict(X_scaled_adapted)

    # Calcul du score de silhouette sur l'échantillon
    sample_labels = kmeans.predict(X_sample_adapted)
    silhouette_avg = silhouette_score(X_sample_adapted, sample_labels)

    inertias.append(kmeans.inertia_)
    silhouette_scores.append(silhouette_avg)

    print(f"(Silhouette: {silhouette_avg:.3f})")

print("\n✅ Calculs terminés")

# Utilisation du module de visualisation optimisé
fig = plot_elbow_curve(k_range, inertias, silhouette_scores)
export_figure(fig, notebook_name="3", export_number=1, base_name="elbow_curve_adapted")

# Affichage des résultats
print(f"\n📊 Inerties par k : {dict(zip(k_range, [round(i, 2) for i in inertias]))}")
print(f"📊 Scores silhouette : {dict(zip(k_range, [round(s, 3) for s in silhouette_scores]))}")

# Calcul des métriques complémentaires
print("\n🔍 Calcul des métriques complémentaires...")

calinski_scores = []

for k in k_range:
    # Recalcul pour obtenir les labels (optimisation possible)
    kmeans = KMeans(n_clusters=k, random_state=SEED, n_init=10, max_iter=300)
    cluster_labels = kmeans.fit_predict(X_scaled)

    # Score de Calinski-Harabasz
    calinski_score = calinski_harabasz_score(X_scaled, cluster_labels)
    calinski_scores.append(calinski_score)

# Visualisation comparative des métriques
fig, axes = plt.subplots(1, 3, figsize=(18, 6))

# Score de silhouette
axes[0].plot(k_range, silhouette_scores, 'go-', linewidth=2, markersize=8)
axes[0].set_title('Score de Silhouette vs K')
axes[0].set_xlabel('Nombre de clusters (K)')
axes[0].set_ylabel('Score de Silhouette')
axes[0].grid(True, alpha=0.3)
axes[0].set_ylim(0, max(silhouette_scores) * 1.1)

# Score de Calinski-Harabasz
axes[1].plot(k_range, calinski_scores, 'mo-', linewidth=2, markersize=8)
axes[1].set_title('Score de Calinski-Harabasz vs K')
axes[1].set_xlabel('Nombre de clusters (K)')
axes[1].set_ylabel('Score de Calinski-Harabasz')
axes[1].grid(True, alpha=0.3)

# Comparaison des métriques normalisées
ax2 = axes[2]
# Normalisation des métriques pour comparaison
inertias_norm = np.array(inertias) / max(inertias)
silhouette_norm = np.array(silhouette_scores) / max(silhouette_scores)

ax2.plot(k_range, 1 - inertias_norm, 'b-', label='1 - Inertie (norm.)', linewidth=2, marker='o')
ax2.plot(k_range, silhouette_norm, 'g-', label='Silhouette (norm.)', linewidth=2, marker='s')
ax2.set_xlabel('Nombre de clusters (K)')
ax2.set_ylabel('Score normalisé')
ax2.set_title('Comparaison des métriques')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
export_figure(plt.gcf(), notebook_name="3", export_number=2, base_name="clustering_metrics")
plt.show()

# Identification du k optimal
optimal_k_silhouette = k_range[np.argmax(silhouette_scores)]
optimal_k_calinski = k_range[np.argmax(calinski_scores)]

print(f"\n📊 Résultats de l'optimisation :")
print(f"   K optimal selon Silhouette : {optimal_k_silhouette} (score = {max(silhouette_scores):.3f})")
print(f"   K optimal selon Calinski-Harabasz : {optimal_k_calinski} (score = {max(calinski_scores):.0f})")
print(f"   Scores Calinski : {dict(zip(k_range, [round(c, 1) for c in calinski_scores]))}")

# Synthèse pour le choix de k
print("\n📋 Synthèse pour le choix du nombre optimal de clusters")

# Tableau de synthèse
results_df = pd.DataFrame({
    'K': k_range,
    'Inertie': inertias,
    'Silhouette': silhouette_scores,
    'Calinski_Harabasz': calinski_scores
})

# Ajout des variations relatives
results_df['Inertie_reduction_%'] = results_df['Inertie'].pct_change().fillna(0).abs() * 100
results_df['Silhouette_rank'] = results_df['Silhouette'].rank(ascending=False)
results_df['Calinski_rank'] = results_df['Calinski_Harabasz'].rank(ascending=False)

print("\n📊 Tableau de synthèse pour le choix de K :")
display(results_df.round(3))

# Analyse automatique du coude
# Calcul de la dérivée seconde pour identifier le coude
if len(inertias) >= 3:
    second_derivatives = np.diff(np.diff(inertias))
    # Le coude correspond au maximum de la dérivée seconde
    elbow_idx = np.argmax(second_derivatives) + 2  # +2 car on a perdu 2 points avec diff
    optimal_k_elbow = k_range[elbow_idx] if elbow_idx < len(k_range) else k_range[-1]
else:
    optimal_k_elbow = k_range[len(k_range)//2]  # Valeur par défaut

# Recommandations basées sur les métriques
print("\n=== 🎯 RECOMMANDATIONS ===")
print(f"📈 Méthode du coude : K = {optimal_k_elbow}")
print(f"🎭 Score de silhouette optimal : K = {optimal_k_silhouette} (score = {max(silhouette_scores):.3f})")
print(f"📊 Score de Calinski-Harabasz optimal : K = {optimal_k_calinski} (score = {max(calinski_scores):.0f})")

# Choix final basé sur la convergence des métriques
# Logique de décision : privilégier la silhouette si cohérente, sinon consensus
if optimal_k_silhouette == optimal_k_calinski:
    optimal_k = optimal_k_silhouette
    justification = "Convergence parfaite entre silhouette et Calinski-Harabasz"
elif abs(optimal_k_silhouette - optimal_k_elbow) <= 1:
    optimal_k = optimal_k_silhouette
    justification = "Silhouette cohérente avec la méthode du coude"
else:
    # Prendre la médiane des recommandations
    recommendations = [optimal_k_elbow, optimal_k_silhouette, optimal_k_calinski]
    optimal_k = int(np.median(recommendations))
    justification = "Consensus entre les différentes métriques"

print(f"\n🎯 CHOIX FINAL : K = {optimal_k}")
print(f"📝 Justification : {justification}")
print(f"\n✅ Nombre de clusters sélectionné pour la suite : {optimal_k}")

cluster_labels = kmeans.predict(X_scaled)
df_complete_clustered = df_complete.copy()
df_complete_clustered['cluster'] = cluster_labels

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

print("\n🔍 Analyse détaillée des clusters...")

# 5.1 Analyse des caractéristiques par cluster
print("\n📊 Caractéristiques moyennes par cluster :")
cluster_analysis = df_complete_clustered.groupby('cluster').agg({
    'recency': 'mean',
    'frequency': 'mean',
    'monetary': 'mean',
    'customer_lifespan_days': 'mean',
    'days_since_first_order': 'mean',
    'total_amount': 'mean',
    'avg_order_value': 'mean',
    'total_orders': 'mean',
    'purchase_frequency': 'mean',
    'order_count': 'mean',
    'montant_moyen': 'mean'
}).round(2)

print(cluster_analysis)

# 5.2 Visualisation des distributions
print("\n📈 Visualisation des distributions par cluster...")

# Sélection des variables importantes pour la visualisation
key_variables = ['recency', 'monetary', 'total_amount', 'avg_order_value']

# Création des boxplots
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

for idx, var in enumerate(key_variables):
    sns.boxplot(x='cluster', y=var, data=df_complete_clustered, ax=axes[idx])
    axes[idx].set_title(f'Distribution de {var} par cluster')
    axes[idx].set_xlabel('Cluster')
    axes[idx].set_ylabel(var)

plt.tight_layout()
export_figure(fig, notebook_name="3", export_number=3, base_name="cluster_distributions")

# 5.3 Détection des outliers
print("\n🔍 Analyse des outliers...")

# Calcul des z-scores
z_scores = stats.zscore(X_scaled)
outliers = (abs(z_scores) > 3).any(axis=1)
outlier_percentage = (outliers.sum() / len(X_scaled)) * 100

print(f"Nombre d'outliers potentiels : {outliers.sum()} ({outlier_percentage:.1f}% des clients)")

# Distribution des outliers par cluster
outlier_clusters = df_complete_clustered.loc[outliers, 'cluster'].value_counts()
print("\nDistribution des outliers par cluster :")
for cluster, count in outlier_clusters.items():
    percentage = (count / len(df_complete_clustered[df_complete_clustered['cluster'] == cluster])) * 100
    print(f"Cluster {cluster}: {count} outliers ({percentage:.1f}% du cluster)")

# 5.4 Analyse des variables catégorielles
print("\n📊 Analyse des variables catégorielles par cluster...")

categorical_vars = ['customer_age_category']  # Seule variable catégorielle disponible

for var in categorical_vars:
    if var in df_complete_clustered.columns:
        print(f"\nDistribution de {var} par cluster :")
        cross_tab = pd.crosstab(df_complete_clustered['cluster'], df_complete_clustered[var], normalize='index') * 100
        print(cross_tab.round(1))

# 5.5 Synthèse des clusters
print("\n📝 Synthèse des clusters :")

for cluster in df_complete_clustered['cluster'].unique():
    cluster_data = df_complete_clustered[df_complete_clustered['cluster'] == cluster]
    print(f"\nCluster {cluster} :")
    print(f"Taille : {len(cluster_data)} clients ({len(cluster_data)/len(df_complete_clustered)*100:.1f}%)")
    print("Caractéristiques principales :")
    for var in key_variables:
        mean_val = cluster_data[var].mean()
        print(f"- {var} : {mean_val:.2f}")

# 🚀 ENTRAÎNEMENT DU MODÈLE K-MEANS "FIRST PURCHASE"
print("🚀 Entraînement du modèle K-Means avec variables adaptées...")

# Vérification du dataset adapté
if 'X_scaled_adapted' not in locals():
    print("❌ Erreur : Variables adaptées non créées. Exécutez la section 1.4 d'abord.")
    raise ValueError("Dataset adapté manquant")

print(f"\n📊 Dataset utilisé : {X_scaled_adapted.shape}")
print(f"   Variables : {list(X_scaled_adapted.columns)}")

# Détermination du k optimal pour les variables adaptées
# Pour les variables "First Purchase", nous utilisons k=3 ou k=4
optimal_k_adapted = 3  # Adapté au contexte mono-achat

# Entraînement avec paramètres optimisés
kmeans_final = KMeans(
    n_clusters=optimal_k_adapted,
    random_state=SEED,
    n_init=10,  # Réduit pour optimiser
    max_iter=300,  # Réduit pour optimiser
    tol=1e-4,  # Augmenté pour optimiser
    algorithm='elkan'  # Plus rapide
)

cluster_labels = kmeans_final.fit_predict(X_scaled_adapted)

# Ajout des labels aux datasets
X_with_clusters = X_scaled_adapted.copy()
X_with_clusters['cluster'] = cluster_labels

# Création d'un dataset avec IDs pour les variables adaptées
X_with_ids_clustered = X_scaled_adapted.copy()
X_with_ids_clustered.index = df_complete.index  # Alignement des index
X_with_ids_clustered['cluster'] = cluster_labels

# Dataset complet avec clusters
df_complete_clustered = df_complete.copy()
df_complete_clustered['cluster'] = cluster_labels

# Métriques finales - calcul sur un échantillon pour la silhouette
sample_size = min(10000, len(X_scaled_adapted))
X_sample_adapted = X_scaled_adapted.sample(n=sample_size, random_state=SEED)
sample_labels = kmeans_final.predict(X_sample_adapted)

final_silhouette = silhouette_score(X_sample_adapted, sample_labels)
final_calinski = calinski_harabasz_score(X_scaled_adapted, cluster_labels)
final_inertia = kmeans_final.inertia_

print(f"\n✅ Clustering \"First Purchase\" réalisé avec K = {optimal_k_adapted}")
print(f"📊 Métriques finales :")
print(f"   Score de silhouette : {final_silhouette:.3f} (calculé sur {sample_size} points)")
print(f"   Score de Calinski-Harabasz : {final_calinski:.1f}")
print(f"   Inertie : {final_inertia:.1f}")

# Distribution des clusters
cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()
print(f"\n📈 Distribution des clusters :")
for cluster_id, count in cluster_counts.items():
    percentage = (count / len(cluster_labels)) * 100
    print(f"   Cluster {cluster_id}: {count:,} clients ({percentage:.1f}%)")

# Vérification de l'équilibre des clusters
min_cluster_size = cluster_counts.min()
max_cluster_size = cluster_counts.max()
balance_ratio = min_cluster_size / max_cluster_size

print(f"\n🔍 Équilibre des clusters :")
print(f"   Ratio min/max : {balance_ratio:.2f}")
if balance_ratio >= 0.3:
    print("   ✅ Clusters bien équilibrés")
elif balance_ratio >= 0.1:
    print("   ⚠️ Clusters moyennement équilibrés")
else:
    print("   ❌ Clusters déséquilibrés - à surveiller")

# Analyse des centres de clusters
print("\n🔍 Analyse des centres de clusters...")

# Récupération des centres
cluster_centers = kmeans_final.cluster_centers_
centers_df = pd.DataFrame(cluster_centers, columns=X_scaled.columns)
centers_df.index = [f'Cluster_{i}' for i in range(optimal_k)]

print("\n📊 Centres des clusters (valeurs normalisées) :")
display(centers_df.round(3))

# Visualisation des centres avec heatmap
fig, axes = plt.subplots(1, 2, figsize=(20, 8))

# Heatmap des centres
sns.heatmap(centers_df.T, annot=True, cmap='RdBu_r', center=0,
            fmt='.2f', cbar_kws={'label': 'Valeur normalisée'},
            ax=axes[0])
axes[0].set_title('Heatmap des centres de clusters\n(Valeurs normalisées)')
axes[0].set_xlabel('Clusters')
axes[0].set_ylabel('Variables')

# Graphique radar des profils (si pas trop de variables)
if len(X_scaled.columns) <= 10:
    # Utilisation du module de visualisation optimisé
    fig_radar = plot_cluster_profiles(centers_df, optimal_k)
    axes[1].remove()  # Supprimer le subplot pour le remplacer
    # Note: le graphique radar sera affiché séparément
else:
    # Graphique en barres pour les moyennes
    centers_df.T.plot(kind='bar', ax=axes[1], width=0.8)
    axes[1].set_title('Profils des clusters (barres)')
    axes[1].set_xlabel('Variables')
    axes[1].set_ylabel('Valeur normalisée')
    axes[1].legend(title='Clusters', bbox_to_anchor=(1.05, 1), loc='upper left')
    axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
export_figure(plt.gcf(), notebook_name="3", export_number=3, base_name="cluster_centers")
plt.show()

# Identification des caractéristiques principales par cluster
print("\n🎯 Caractéristiques principales par cluster :")
for i, cluster_name in enumerate(centers_df.index):
    center = centers_df.iloc[i]
    high_features = center[center > 0.5].sort_values(ascending=False)
    low_features = center[center < -0.5].sort_values()
    moderate_high = center[(center > 0.2) & (center <= 0.5)].sort_values(ascending=False)
    moderate_low = center[(center < -0.2) & (center >= -0.5)].sort_values()

    print(f"\n📋 {cluster_name} ({cluster_counts[i]:,} clients):")
    if len(high_features) > 0:
        print(f"   🔴 Très élevé: {', '.join(high_features.index[:3])}")
    if len(moderate_high) > 0:
        print(f"   🟠 Élevé: {', '.join(moderate_high.index[:3])}")
    if len(moderate_low) > 0:
        print(f"   🟡 Faible: {', '.join(moderate_low.index[:3])}")
    if len(low_features) > 0:
        print(f"   🔵 Très faible: {', '.join(low_features.index[:3])}")

# Sauvegarde du modèle entraîné
print("\n💾 Sauvegarde du modèle et des métadonnées...")

# Création du dossier models s'il n'existe pas
os.makedirs('reports/models', exist_ok=True)

# Sauvegarde du modèle
model_path = 'reports/models/3_01_kmeans_segmentation.pkl'
joblib.dump(kmeans_final, model_path)
print(f"✅ Modèle sauvegardé : {model_path}")

# Sauvegarde des métadonnées du modèle
model_metadata = {
    'model_type': 'KMeans',
    'n_clusters': optimal_k,
    'features_used': list(X_scaled.columns),
    'n_samples': len(X_scaled),
    'silhouette_score': final_silhouette,
    'calinski_harabasz_score': final_calinski,
    'inertia': final_inertia,
    'random_state': SEED,
    'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    'cluster_distribution': cluster_counts.to_dict(),
    'optimization_results': {
        'k_range_tested': list(k_range),
        'optimal_k_silhouette': optimal_k_silhouette,
        'optimal_k_calinski': optimal_k_calinski,
        'justification': justification
    }
}

metadata_path = 'reports/models/3_01_kmeans_metadata.json'
with open(metadata_path, 'w') as f:
    json.dump(model_metadata, f, indent=2, default=str)

print(f"✅ Métadonnées sauvegardées : {metadata_path}")

# 🎯 INTERPRÉTATION BUSINESS DES SEGMENTS "FIRST PURCHASE"
print("🎯 INTERPRÉTATION BUSINESS DES SEGMENTS \"FIRST PURCHASE\"")
print("=" * 70)

# Analyse des centres de clusters pour interprétation
centers_df = pd.DataFrame(
    kmeans_final.cluster_centers_,
    columns=X_scaled_adapted.columns,
    index=[f'Segment_{i}' for i in range(optimal_k_adapted)]
)

print(f"\n📊 Centres des segments (valeurs normalisées) :")
display(centers_df.round(3))

# Interprétation business selon la stratégie
print(f"\n🔍 INTERPRÉTATION BUSINESS SELON LA STRATÉGIE :")

segment_interpretations = {}

for i in range(optimal_k_adapted):
    recency_val = centers_df.loc[f'Segment_{i}', 'recency']
    monetary_val = centers_df.loc[f'Segment_{i}', 'monetary']

    # Logique d'interprétation selon la stratégie \"First Purchase\"
    if recency_val < -0.5:  # Récence faible (achat récent)
        if monetary_val > 0.5:  # Montant élevé
            segment_name = \"🌟 Nouveaux Clients Premium\"
            description = \"Clients récents avec premier achat élevé - Potentiel de fidélisation maximal\"
            strategy = \"Programmes VIP, recommandations premium, service client prioritaire\"
        elif monetary_val > -0.5:  # Montant moyen
            segment_name = \"💎 Nouveaux Clients Prometteurs\"
            description = \"Clients récents avec premier achat moyen - Bon potentiel\"
            strategy = \"Offres de montée en gamme, programmes de fidélité, cross-selling\"
        else:  # Montant faible
            segment_name = \"🌱 Nouveaux Clients Découverte\"
            description = \"Clients récents avec petit premier achat - À développer\"
            strategy = \"Offres d'essai, bundles attractifs, réductions ciblées\"
    else:  # Récence élevée (achat ancien)
        if monetary_val > 0.5:  # Montant élevé
            segment_name = \"⚠️ Clients Premium Dormants\"
            description = \"Clients à fort potentiel mais inactifs - Réactivation urgente\"
            strategy = \"Campagnes de réactivation premium, offres exclusives, contact direct\"
        else:  # Montant faible/moyen
            segment_name = \"😴 Clients Inactifs\"
            description = \"Clients anciens avec faible engagement - Réactivation difficile\"
            strategy = \"Campagnes de reconquête, offres agressives, enquêtes de satisfaction\"

    segment_interpretations[i] = {
        'name': segment_name,
        'description': description,
        'strategy': strategy,
        'recency': recency_val,
        'monetary': monetary_val
    }

# Affichage des interprétations
for i, interp in segment_interpretations.items():
    count = (cluster_labels == i).sum()
    percentage = (count / len(cluster_labels)) * 100

    print(f"\n{interp['name']} (Segment {i})")
    print(f"   📊 Taille : {count:,} clients ({percentage:.1f}%)")
    print(f"   📝 Description : {interp['description']}")
    print(f"   🎯 Stratégie : {interp['strategy']}")
    print(f"   📈 Profil : Récence={interp['recency']:.2f}, Montant={interp['monetary']:.2f}")

print(f"\n✅ Interprétation business complétée selon la stratégie \"First Purchase\"")

# 📋 RECOMMANDATIONS STRATÉGIQUES DÉTAILLÉES
print("📋 RECOMMANDATIONS STRATÉGIQUES DÉTAILLÉES")
print("=" * 50)

# Analyse détaillée par segment
for i, interp in segment_interpretations.items():
    segment_data = df_complete_clustered[df_complete_clustered['cluster'] == i]
    count = len(segment_data)

    print(f"\n{interp['name']} - {count:,} clients")
    print("=" * 60)

    # Statistiques du segment
    if 'monetary' in df_complete_clustered.columns:
        avg_monetary = segment_data['monetary'].mean()
        print(f"   💰 Montant moyen : {avg_monetary:.2f}")

    if 'recency' in df_complete_clustered.columns:
        avg_recency = segment_data['recency'].mean()
        print(f"   📅 Récence moyenne : {avg_recency:.2f} jours")

    # Recommandations spécifiques
    print(f"\n   🎯 ACTIONS PRIORITAIRES :")

    if \"Premium\" in interp['name']:
        print(f"      1. Programme VIP avec avantages exclusifs")
        print(f"      2. Service client dédié et prioritaire")
        print(f"      3. Recommandations produits haut de gamme")
        print(f"      4. Invitations événements exclusifs")
    elif \"Prometteurs\" in interp['name']:
        print(f"      1. Offres de montée en gamme ciblées")
        print(f"      2. Programme de fidélité attractif")
        print(f"      3. Cross-selling intelligent")
        print(f"      4. Communication personnalisée")
    elif \"Découverte\" in interp['name']:
        print(f"      1. Offres d'essai et échantillons gratuits")
        print(f"      2. Bundles produits attractifs")
        print(f"      3. Réductions sur deuxième achat")
        print(f"      4. Contenu éducatif sur les produits")
    elif \"Dormants\" in interp['name']:
        print(f"      1. Campagne de réactivation urgente")
        print(f"      2. Offres exclusives limitées dans le temps")
        print(f"      3. Contact direct personnalisé")
        print(f"      4. Enquête de satisfaction approfondie")
    else:  # Inactifs
        print(f"      1. Campagne de reconquête agressive")
        print(f"      2. Offres de retour très attractives")
        print(f"      3. Enquête sur les raisons d'abandon")
        print(f"      4. Réduction des coûts d'acquisition")

    print(f"\n   📊 KPIs À SUIVRE :")
    print(f"      • Taux de conversion sur offres ciblées")
    print(f"      • Évolution du panier moyen")
    print(f"      • Taux de rétention à 3/6/12 mois")
    print(f"      • Satisfaction client (NPS)")

print(f"\n🎯 CONCLUSION STRATÉGIQUE :")
print(f"   La segmentation \"First Purchase\" permet une approche ciblée")
print(f"   basée sur le potentiel de chaque client dès son premier achat.")
print(f"   Focus sur la rétention et le développement de la valeur client.")

# 🔍 RÉDUCTION DE DIMENSION AVEC PCA - VARIABLES ADAPTÉES
print("🔍 Application de l'ACP pour la visualisation des variables adaptées...")

# Vérification du dataset adapté
if 'X_scaled_adapted' not in locals():
    print("❌ Erreur : Variables adaptées non créées. Exécutez la section 1.4 d'abord.")
    raise ValueError("Dataset adapté manquant")

print(f"\n📊 Dataset utilisé : {X_scaled_adapted.shape}")
print(f"   Variables : {list(X_scaled_adapted.columns)}")

# PCA 2D pour visualisation
pca_2d = PCA(n_components=2, random_state=SEED)
X_pca_2d = pca_2d.fit_transform(X_scaled_adapted)

# PCA 3D pour visualisation avancée
pca_3d = PCA(n_components=3, random_state=SEED)
X_pca_3d = pca_3d.fit_transform(X_scaled_adapted)

# Variance expliquée
variance_2d = pca_2d.explained_variance_ratio_.sum()
variance_3d = pca_3d.explained_variance_ratio_.sum()

print(f"📊 Variance expliquée :")
print(f"   PCA 2D : {variance_2d:.1%} (PC1: {pca_2d.explained_variance_ratio_[0]:.1%}, PC2: {pca_2d.explained_variance_ratio_[1]:.1%})")
print(f"   PCA 3D : {variance_3d:.1%} (PC3: {pca_3d.explained_variance_ratio_[2]:.1%})")

# Évaluation de la qualité de la réduction
if variance_2d >= 0.6:
    print("   ✅ Excellente représentation en 2D")
elif variance_2d >= 0.4:
    print("   ⚠️ Représentation correcte en 2D")
else:
    print("   ❌ Représentation limitée en 2D - considérer plus de dimensions")

# Création des DataFrames pour visualisation
pca_2d_df = pd.DataFrame({
    'PC1': X_pca_2d[:, 0],
    'PC2': X_pca_2d[:, 1],
    'cluster': cluster_labels
})

pca_3d_df = pd.DataFrame({
    'PC1': X_pca_3d[:, 0],
    'PC2': X_pca_3d[:, 1],
    'PC3': X_pca_3d[:, 2],
    'cluster': cluster_labels
})

# Analyse des contributions des variables aux composantes principales
print("\n🔍 Contributions principales aux composantes :")
components_df = pd.DataFrame(
    pca_2d.components_.T,
    columns=['PC1', 'PC2'],
    index=X_scaled_adapted.columns
)

# Variables les plus contributives pour PC1 et PC2
pc1_contrib = components_df['PC1'].abs().sort_values(ascending=False)
pc2_contrib = components_df['PC2'].abs().sort_values(ascending=False)

print(f"   PC1 ({pca_2d.explained_variance_ratio_[0]:.1%}): {', '.join(pc1_contrib.head(3).index)}")
print(f"   PC2 ({pca_2d.explained_variance_ratio_[1]:.1%}): {', '.join(pc2_contrib.head(3).index)}")

print(f"\n📊 INTERPRÉTATION DES COMPOSANTES PRINCIPALES :")
print(f"   PC1 représente principalement : {pc1_contrib.index[0]}")
print(f"   PC2 représente principalement : {pc2_contrib.index[0]}")
print(f"   Variance totale expliquée : {variance_2d:.1%}")

# Visualisation 2D des clusters
print("\n📊 Création des visualisations 2D des clusters...")

export_figure(fig, notebook_name="3", export_number=4, base_name="clusters_2d_pca")

# Visualisation alternative avec seaborn pour plus de détails
fig, axes = plt.subplots(1, 2, figsize=(16, 6))

# Scatterplot avec seaborn
sns.scatterplot(
    data=pca_2d_df,
    x='PC1', y='PC2',
    hue='cluster',
    palette='Set2',
    alpha=0.7,
    s=60,
    ax=axes[0]
)
axes[0].set_xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.1%} de variance)')
axes[0].set_ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.1%} de variance)')
axes[0].set_title('Clusters en 2D (PCA) - Vue détaillée')
axes[0].grid(True, alpha=0.3)

# Ajout des centres
centers_pca = pca_2d.transform(cluster_centers)
axes[0].scatter(centers_pca[:, 0], centers_pca[:, 1],
               c='red', marker='X', s=200, linewidth=2,
               label='Centres', edgecolors='black')
axes[0].legend()

# Graphique de densité
for i in range(optimal_k):
    mask = cluster_labels == i
    axes[1].scatter(X_pca_2d[mask, 0], X_pca_2d[mask, 1],
                   alpha=0.3, s=30, label=f'Cluster {i}')

# Ellipses de confiance (optionnel)
from matplotlib.patches import Ellipse
for i in range(optimal_k):
    mask = cluster_labels == i
    if np.sum(mask) > 1:  # Au moins 2 points pour calculer la covariance
        cluster_data = X_pca_2d[mask]
        mean = cluster_data.mean(axis=0)
        cov = np.cov(cluster_data.T)

        # Calcul des axes de l'ellipse
        eigenvals, eigenvecs = np.linalg.eigh(cov)
        angle = np.degrees(np.arctan2(eigenvecs[1, 0], eigenvecs[0, 0]))
        width, height = 2 * np.sqrt(eigenvals)

        ellipse = Ellipse(mean, width, height, angle=angle,
                         alpha=0.2, facecolor=plt.cm.Set2(i/optimal_k))
        axes[1].add_patch(ellipse)

axes[1].set_xlabel(f'PC1 ({pca_2d.explained_variance_ratio_[0]:.1%} de variance)')
axes[1].set_ylabel(f'PC2 ({pca_2d.explained_variance_ratio_[1]:.1%} de variance)')
axes[1].set_title('Clusters avec ellipses de confiance')
axes[1].grid(True, alpha=0.3)
axes[1].legend()

plt.tight_layout()
export_figure(plt.gcf(), notebook_name="3", export_number=5, base_name="clusters_detailed")
plt.show()

# Visualisation 3D des clusters
print("\n📊 Création de la visualisation 3D des clusters...")

# Graphique 3D avec matplotlib
from mpl_toolkits.mplot3d import Axes3D

fig = plt.figure(figsize=(14, 10))
ax = fig.add_subplot(111, projection='3d')

# Palette de couleurs
colors = plt.cm.tab10(np.linspace(0, 1, optimal_k))

# Affichage des points par cluster
for i in range(optimal_k):
    mask = cluster_labels == i
    ax.scatter(X_pca_3d[mask, 0], X_pca_3d[mask, 1], X_pca_3d[mask, 2],
              c=[colors[i]], label=f'Cluster {i}', alpha=0.6, s=50)

# Centres en 3D
centers_pca_3d = pca_3d.transform(cluster_centers)
ax.scatter(centers_pca_3d[:, 0], centers_pca_3d[:, 1], centers_pca_3d[:, 2],
          c='red', marker='X', s=300, linewidth=2,
          label='Centres', edgecolors='black')

# Labels et titre
ax.set_xlabel(f'PC1 ({pca_3d.explained_variance_ratio_[0]:.1%})')
ax.set_ylabel(f'PC2 ({pca_3d.explained_variance_ratio_[1]:.1%})')
ax.set_zlabel(f'PC3 ({pca_3d.explained_variance_ratio_[2]:.1%})')
ax.set_title(f'Visualisation 3D des clusters (PCA)\nVariance expliquée: {variance_3d:.1%}')

# Légende
ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

# Amélioration de l'affichage
ax.grid(True, alpha=0.3)

plt.tight_layout()
export_figure(plt.gcf(), notebook_name="3", export_number=6, base_name="clusters_3d_pca")
plt.show()

# Statistiques sur la séparation 3D
print(f"\n📊 Qualité de la visualisation 3D :")
print(f"   Variance expliquée totale : {variance_3d:.1%}")
print(f"   Gain par rapport à 2D : {variance_3d - variance_2d:.1%}")

if variance_3d - variance_2d > 0.1:
    print("   ✅ La 3D apporte une information significative")
else:
    print("   ⚠️ La 3D apporte peu d'information supplémentaire")

# Analyse descriptive par cluster (données originales)
print("\n📊 Analyse descriptive détaillée par cluster...")

# Sélection des variables clés pour l'analyse
key_variables = [
    'recency', 'frequency', 'monetary_total', 'monetary_avg',
    'customer_lifespan_days', 'days_since_first_order',
    'avg_days_between_orders'
]

# Vérification de la présence des variables
available_vars = [var for var in key_variables if var in df_complete_clustered.columns]
print(f"Variables disponibles pour l'analyse : {available_vars}")

# Agrégation sur les données originales (non normalisées)
agg_dict = {}
for var in available_vars:
    agg_dict[var] = ['mean', 'median', 'std', 'min', 'max']

# Ajout du comptage
if 'customer_id' in df_complete_clustered.columns:
    agg_dict['customer_id'] = 'count'
else:
    # Utiliser l'index pour compter
    df_complete_clustered['_count'] = 1
    agg_dict['_count'] = 'sum'

cluster_analysis = df_complete_clustered.groupby('cluster').agg(agg_dict).round(2)

# Aplatir les colonnes multi-index
cluster_analysis.columns = ['_'.join(col).strip() for col in cluster_analysis.columns]

# Renommer la colonne de comptage
count_col = 'customer_id_count' if 'customer_id_count' in cluster_analysis.columns else '_count_sum'
if count_col in cluster_analysis.columns:
    cluster_analysis = cluster_analysis.rename(columns={count_col: 'cluster_size'})

print("\n📋 Analyse descriptive par cluster (valeurs originales) :")
display(cluster_analysis)

# Sauvegarde de l'analyse détaillée
cluster_analysis.to_csv('reports/analysis/3_02_cluster_analysis_detailed.csv')
print(f"\n💾 Analyse détaillée sauvegardée : reports/analysis/3_02_cluster_analysis_detailed.csv")

# Tableaux de synthèse des profils
print("\n📊 Création du tableau de synthèse des segments...")

# Identification des colonnes de moyennes disponibles
mean_columns = [col for col in cluster_analysis.columns if col.endswith('_mean')]
print(f"Colonnes de moyennes disponibles : {mean_columns}")

# Création du tableau simplifié
summary_columns = []
summary_labels = []

# Mapping des colonnes vers des noms lisibles
column_mapping = {
    'recency_mean': 'Récence_moy',
    'frequency_mean': 'Fréquence_moy',
    'monetary_total_mean': 'Montant_total_moy',
    'monetary_avg_mean': 'Montant_moy_moy',
    'customer_lifespan_days_mean': 'Ancienneté_moy'
}

for col, label in column_mapping.items():
    if col in cluster_analysis.columns:
        summary_columns.append(col)
        summary_labels.append(label)

# Ajout de la taille des clusters
if 'cluster_size' in cluster_analysis.columns:
    summary_columns.append('cluster_size')
    summary_labels.append('Taille_cluster')

# Création du tableau de synthèse
summary_table = cluster_analysis[summary_columns].copy()
summary_table.columns = summary_labels

# Ajout de pourcentages si on a la taille des clusters
if 'Taille_cluster' in summary_table.columns:
    summary_table['%_clients'] = (summary_table['Taille_cluster'] / summary_table['Taille_cluster'].sum() * 100).round(1)

print("\n📋 Tableau de synthèse des segments :")
display(summary_table)

# Sauvegarde du tableau de synthèse
summary_table.to_csv('reports/analysis/3_03_cluster_summary.csv')
print(f"\n💾 Tableau de synthèse sauvegardé : reports/analysis/3_03_cluster_summary.csv")

# Identification et nommage des profils clients
print("\n🎯 Identification automatique des profils clients...")

# Analyse automatique des profils
profile_descriptions = {}

for cluster_id in range(optimal_k):
    cluster_data = summary_table.iloc[cluster_id]
    print(f"\n--- Cluster {cluster_id} ---")
    print(cluster_data)  # Debug : affiche toutes les valeurs

    metrics = {}
    if 'Récence_moy' in cluster_data.index:
        metrics['recency'] = cluster_data['Récence_moy']
    if 'Fréquence_moy' in cluster_data.index:
        metrics['frequency'] = cluster_data['Fréquence_moy']
    if 'Montant_total_moy' in cluster_data.index:
        metrics['monetary'] = cluster_data['Montant_total_moy']
    if 'Montant_moy_moy' in cluster_data.index:
        metrics['avg_order'] = cluster_data['Montant_moy_moy']

    # Classification plus souple
    profile = "Segment Indéterminé"
    r = metrics.get('recency', None)
    f = metrics.get('frequency', None)
    m = metrics.get('monetary', None)

    if r is not None and f is not None and m is not None:
        if r <= 50 and f >= 3 and m >= 200:
            profile = "🌟 Champions (Fidèles & Actifs)"
        elif r <= 100 and m >= 300:
            profile = "💎 Clients à Forte Valeur"
        elif f >= 4:
            profile = "🔄 Acheteurs Fréquents"
        elif r >= 200:
            profile = "😴 Clients Inactifs/Perdus"
        elif m <= 100:
            profile = "🛒 Petits Acheteurs"
        elif r <= 100:
            profile = "⭐ Clients Récents"
        else:
            profile = "📅 Clients Occasionnels"
    elif r is not None and f is not None:
        if r >= 200:
            profile = "😴 Clients Inactifs/Perdus"
        elif f == 1:
            profile = "🛒 Acheteurs uniques"
        elif f >= 4:
            profile = "🔄 Acheteurs Fréquents"
        elif r <= 100:
            profile = "⭐ Clients Récents"
        else:
            profile = "📅 Clients Occasionnels"
    # Ajoutez d'autres cas selon les métriques disponibles

    profile_descriptions[cluster_id] = profile

# Affichage des profils identifiés
print("\n=== 🎯 PROFILS CLIENTS IDENTIFIÉS ===")
for cluster_id, profile in profile_descriptions.items():
    cluster_data = summary_table.iloc[cluster_id]
    print(f"\n📊 Cluster {cluster_id}: {profile}")

    if 'Taille_cluster' in cluster_data.index:
        size = cluster_data['Taille_cluster']
        pct = cluster_data.get('%_clients', 0)
        print(f"   👥 Taille: {size:.0f} clients ({pct:.1f}%)")

    # Affichage des métriques disponibles
    for metric, label in [('Récence_moy', 'Récence'), ('Fréquence_moy', 'Fréquence'),
                         ('Montant_total_moy', 'Valeur totale'), ('Montant_moy_moy', 'Panier moyen')]:
        if metric in cluster_data.index:
            value = cluster_data[metric]
            unit = ' jours' if 'Récence' in label else (' achats' if 'Fréquence' in label else '€')
            print(f"   📈 {label}: {value:.1f}{unit}")

# Sauvegarde des résultats de clustering
print("\n💾 Sauvegarde des résultats de clustering...")

# Création des dossiers nécessaires
os.makedirs('data/processed', exist_ok=True)
os.makedirs('reports/analysis', exist_ok=True)

# Ajout des profils au dataset complet
df_complete_clustered['cluster_profile'] = df_complete_clustered['cluster'].map(profile_descriptions)

# Sauvegarde des datasets clusterisés
df_complete_clustered.to_csv('data/processed/3_04_customers_clustered.csv', index=False)
df_complete_clustered.to_pickle('data/processed/3_04_customers_clustered.pkl')

# Dataset résumé pour analyse rapide
base_columns = ['cluster', 'cluster_profile']
available_columns = [col for col in ['customer_id', 'recency', 'frequency', 'monetary_total', 'monetary_avg']
                    if col in df_complete_clustered.columns]

summary_columns = base_columns + available_columns
summary_for_export = df_complete_clustered[summary_columns].copy()

summary_for_export.to_csv('data/processed/3_04_customer_segments_summary.csv', index=False)

print("✅ Datasets sauvegardés :")
print("   - 3_04_customers_clustered.pkl : dataset complet avec clusters")
print("   - 3_04_customer_segments_summary.csv : résumé des segments")
print(f"\n📊 Nombre de clients segmentés : {len(df_complete_clustered):,}")
print(f"📊 Nombre de segments créés : {optimal_k}")

# Export des analyses et statistiques
print("\n📊 Export des analyses et statistiques complètes...")

# Export des informations de clustering complètes
clustering_info = {
    'clustering_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    'algorithm': 'KMeans',
    'n_clusters': optimal_k,
    'silhouette_score': final_silhouette,
    'calinski_harabasz_score': final_calinski,
    'inertia': final_inertia,
    'features_used': list(X_scaled.columns),
    'cluster_profiles': profile_descriptions,
    'optimization_process': {
        'k_range_tested': list(k_range),
        'inertias': inertias,
        'silhouette_scores': silhouette_scores,
        'calinski_scores': calinski_scores,
        'optimal_k_silhouette': optimal_k_silhouette,
        'optimal_k_calinski': optimal_k_calinski,
        'selection_justification': justification
    },
    'pca_analysis': {
        'variance_explained_2d': variance_2d,
        'variance_explained_3d': variance_3d,
        'components_2d': pca_2d.explained_variance_ratio_.tolist(),
        'components_3d': pca_3d.explained_variance_ratio_.tolist()
    },
    'cluster_distribution': cluster_counts.to_dict(),
    'balance_ratio': balance_ratio
}

# Ajout des statistiques du tableau de synthèse si disponible
if 'Taille_cluster' in summary_table.columns:
    clustering_info['cluster_sizes'] = summary_table['Taille_cluster'].to_dict()
if '%_clients' in summary_table.columns:
    clustering_info['cluster_percentages'] = summary_table['%_clients'].to_dict()

# Sauvegarde du fichier JSON complet
with open('reports/analysis/3_05_clustering_results_complete.json', 'w') as f:
    json.dump(clustering_info, f, indent=2, default=str)

print("✅ Analyses complètes sauvegardées :")
print("   - 3_02_cluster_analysis_detailed.csv : statistiques détaillées")
print("   - 3_03_cluster_summary.csv : tableau de synthèse")
print("   - 3_05_clustering_results_complete.json : informations complètes")
print("   - 3_01_kmeans_metadata.json : métadonnées du modèle")

print(f"\n🎯 Clustering terminé avec succès !")
print(f"   📊 {optimal_k} segments identifiés")
print(f"   📈 Score de silhouette : {final_silhouette:.3f}")
print(f"   🎨 {len([f for f in os.listdir('reports/figures') if f.startswith('3_')])} visualisations générées")